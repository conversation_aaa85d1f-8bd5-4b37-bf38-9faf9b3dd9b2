import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Shield,
  Award,
  Edit,
  Trash2,
  Eye,
  Search,
  Calendar,
  User,
  GraduationCap,
  MapPin,
  Save,
  X,
  Plus,
  Download,
  RefreshCw,
  AlertTriangle,
  Lock,
  EyeOff,
  LogOut,
  FileSpreadsheet
} from 'lucide-react';
import { collection, getDocs, doc, updateDoc, deleteDoc, query, orderBy, where } from 'firebase/firestore';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { db, auth } from '@/lib/firebase';
import * as XLSX from 'xlsx';

interface CertificateData {
  id: string;
  certificateId: string;
  name: string;
  age: number;
  courseName: string;
  college: string;
  country: string;
  state: string;
  courseStartDate?: string;
  courseJoiningDate?: string;
  courseEndDate: string;
  certificateProvideDate: string;
  createdAt: any;
  createdBy: string;
  status: string;
}

// Admin credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Tamilselvanadmin6379869678@";

const AdminDashboard = () => {
  const [certificates, setCertificates] = useState<CertificateData[]>([]);
  const [filteredCertificates, setFilteredCertificates] = useState<CertificateData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<CertificateData>>({});
  const [viewingId, setViewingId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Authentication states
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginEmail, setLoginEmail] = useState("");
  const [loginPassword, setLoginPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const { toast } = useToast();

  // Handle keyboard shortcuts for modals
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (viewingId) {
          setViewingId(null);
        } else if (editingId) {
          cancelEdit();
        }
      }
    };

    if (viewingId || editingId) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [viewingId, editingId]);

  // Admin login handler
  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAuthenticating(true);

    try {
      // Validate credentials
      if (loginEmail !== ADMIN_EMAIL || loginPassword !== ADMIN_PASSWORD) {
        throw new Error("Invalid admin credentials. Access denied.");
      }

      // If Firebase is configured, try Firebase authentication
      if (auth) {
        try {
          await signInWithEmailAndPassword(auth, loginEmail, loginPassword);
        } catch (firebaseError: any) {
          if (firebaseError.code === 'auth/user-not-found') {
            throw new Error("Admin user not found in Firebase. Please create the admin user in Firebase Console first.");
          } else if (firebaseError.code === 'auth/wrong-password') {
            throw new Error("Wrong password. Please check your credentials.");
          } else if (firebaseError.code === 'auth/invalid-email') {
            throw new Error("Invalid email format.");
          } else if (firebaseError.code === 'auth/too-many-requests') {
            throw new Error("Too many failed attempts. Please try again later.");
          } else {
            throw new Error(`Firebase Authentication Error: ${firebaseError.message}`);
          }
        }
      } else {
        console.warn("Firebase not configured. Using local authentication only.");
        toast({
          title: "Warning",
          description: "Firebase not configured. Some features may not work properly.",
          variant: "destructive",
        });
      }

      setIsAuthenticated(true);
      toast({
        title: "Authentication Successful",
        description: "Welcome to Admin Dashboard",
      });
    } catch (error: any) {
      console.error("Authentication error:", error);
      toast({
        title: "Authentication Failed",
        description: error.message || "Invalid admin credentials. Access denied.",
        variant: "destructive",
      });
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Logout handler
  const handleLogout = () => {
    setIsAuthenticated(false);
    setLoginEmail("");
    setLoginPassword("");
    toast({
      title: "Logged Out",
      description: "You have been successfully logged out.",
    });
  };

  // Fetch all certificates
  const fetchCertificates = async () => {
    setIsLoading(true);
    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      const q = query(collection(db, 'certificates'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      const certificatesData: CertificateData[] = [];
      querySnapshot.forEach((doc) => {
        certificatesData.push({
          id: doc.id,
          ...doc.data()
        } as CertificateData);
      });

      setCertificates(certificatesData);
      setFilteredCertificates(certificatesData);
      
      toast({
        title: "Data Loaded",
        description: `Found ${certificatesData.length} certificates`,
      });
    } catch (error: any) {
      console.error("Error fetching certificates:", error);
      toast({
        title: "Error",
        description: "Failed to load certificates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter certificates
  useEffect(() => {
    let filtered = certificates;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(cert => 
        cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.certificateId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.college.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(cert => cert.status === statusFilter);
    }

    setFilteredCertificates(filtered);
  }, [searchTerm, statusFilter, certificates]);

  // Load certificates on component mount
  useEffect(() => {
    fetchCertificates();
  }, []);

  // Start editing
  const startEdit = (certificate: CertificateData) => {
    setEditingId(certificate.id);
    setEditForm(certificate);
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingId(null);
    setEditForm({});
  };

  // Save changes
  const saveChanges = async () => {
    if (!editingId || !editForm) return;

    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      const docRef = doc(db, 'certificates', editingId);
      await updateDoc(docRef, {
        ...editForm,
        updatedAt: new Date(),
        updatedBy: "admin"
      });

      // Update local state
      setCertificates(prev => 
        prev.map(cert => 
          cert.id === editingId ? { ...cert, ...editForm } : cert
        )
      );

      setEditingId(null);
      setEditForm({});

      toast({
        title: "Certificate Updated",
        description: "Certificate information has been successfully updated.",
      });
    } catch (error: any) {
      console.error("Error updating certificate:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update certificate. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Delete certificate
  const deleteCertificate = async (id: string, certificateId: string) => {
    if (!confirm(`Are you sure you want to delete certificate ${certificateId}? This action cannot be undone.`)) {
      return;
    }

    setIsDeleting(id);
    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      await deleteDoc(doc(db, 'certificates', id));

      // Update local state
      setCertificates(prev => prev.filter(cert => cert.id !== id));

      toast({
        title: "Certificate Deleted",
        description: `Certificate ${certificateId} has been permanently deleted.`,
      });
    } catch (error: any) {
      console.error("Error deleting certificate:", error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "Unknown";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Export to Excel function
  const exportToExcel = () => {
    try {
      // Prepare data for export with proper formatting
      const exportData = filteredCertificates.map((cert, index) => ({
        'S.No': index + 1,
        'Certificate ID': cert.certificateId || '',
        'Student Name': cert.name || '',
        'Age': cert.age || '',
        'Course Name': cert.courseName || '',
        'College/Institution': cert.college || '',
        'Country': cert.country || '',
        'State/Province': cert.state || '',
        'Course Start Date': cert.courseStartDate ? formatDate(cert.courseStartDate) : 'Not specified',
        'Course Joining Date': cert.courseJoiningDate ? formatDate(cert.courseJoiningDate) : 'Not specified',
        'Course End Date': cert.courseEndDate ? formatDate(cert.courseEndDate) : 'Not specified',
        'Certificate Provide Date': cert.certificateProvideDate ? formatDate(cert.certificateProvideDate) : 'Not specified',
        'Status': cert.status || '',
        'Created At': formatTimestamp(cert.createdAt),
        'Created By': cert.createdBy || '',
        'Document ID': cert.id || ''
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // Set column widths for better alignment
      const colWidths = [
        { wch: 8 },   // S.No
        { wch: 20 },  // Certificate ID
        { wch: 25 },  // Student Name
        { wch: 8 },   // Age
        { wch: 30 },  // Course Name
        { wch: 35 },  // College/Institution
        { wch: 15 },  // Country
        { wch: 20 },  // State/Province
        { wch: 18 },  // Course Start Date
        { wch: 18 },  // Course Joining Date
        { wch: 18 },  // Course End Date
        { wch: 20 },  // Certificate Provide Date
        { wch: 12 },  // Status
        { wch: 20 },  // Created At
        { wch: 15 },  // Created By
        { wch: 25 }   // Document ID
      ];
      ws['!cols'] = colWidths;

      // Style the header row
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
          font: { bold: true, color: { rgb: "FFFFFF" } },
          fill: { fgColor: { rgb: "366092" } },
          alignment: { horizontal: "center", vertical: "center" },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }

      // Add borders and alignment to all cells
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) continue;

          if (!ws[cellAddress].s) ws[cellAddress].s = {};
          ws[cellAddress].s.border = {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          };

          // Center align specific columns
          if (col === 0 || col === 3 || col === 12) { // S.No, Age, Status
            ws[cellAddress].s.alignment = { horizontal: "center", vertical: "center" };
          } else {
            ws[cellAddress].s.alignment = { horizontal: "left", vertical: "center" };
          }
        }
      }

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, "Certificates Data");

      // Generate filename with current date
      const currentDate = new Date().toISOString().split('T')[0];
      const filename = `Certificates_Export_${currentDate}.xlsx`;

      // Save the file
      XLSX.writeFile(wb, filename);

      toast({
        title: "Export Successful",
        description: `${exportData.length} certificates exported to ${filename}`,
      });
    } catch (error: any) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export certificates. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Admin login form
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center p-4">
        <Card className="w-full max-w-md glassmorphism">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mb-4">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-white">Admin Access</CardTitle>
            <CardDescription className="text-gray-300">
              Admin Dashboard - Authorized Personnel Only
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAdminLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white flex items-center gap-2">
                  <User size={16} />
                  Admin Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={loginEmail}
                  onChange={(e) => setLoginEmail(e.target.value)}
                  placeholder="Enter admin email"
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white flex items-center gap-2">
                  <Lock size={16} />
                  Admin Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                    placeholder="Enter admin password"
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-gray-400 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </Button>
                </div>
              </div>
              <Button
                type="submit"
                className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white"
                disabled={isAuthenticating}
              >
                {isAuthenticating ? "Authenticating..." : "Access Admin Dashboard"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
              <p className="text-gray-400">Certificate Management & History</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={exportToExcel}
              variant="outline"
              className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
              disabled={filteredCertificates.length === 0}
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Export Excel
            </Button>
            <Button
              onClick={fetchCertificates}
              variant="outline"
              className="border-psyco-green-DEFAULT/50 text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="border-red-500/50 text-red-400 hover:bg-red-500/10"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Certificates</p>
                  <p className="text-2xl font-bold text-white">{certificates.length}</p>
                </div>
                <Award className="h-8 w-8 text-psyco-green-DEFAULT" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Active Certificates</p>
                  <p className="text-2xl font-bold text-white">
                    {certificates.filter(c => c.status === 'active').length}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">This Month</p>
                  <p className="text-2xl font-bold text-white">
                    {certificates.filter(c => {
                      const created = c.createdAt?.toDate ? c.createdAt.toDate() : new Date(c.createdAt);
                      const thisMonth = new Date();
                      return created.getMonth() === thisMonth.getMonth() && 
                             created.getFullYear() === thisMonth.getFullYear();
                    }).length}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Export Ready</p>
                  <p className="text-2xl font-bold text-white">{filteredCertificates.length}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {filteredCertificates.length > 0 ? 'Ready for Excel export' : 'No data to export'}
                  </p>
                </div>
                <FileSpreadsheet className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter Controls */}
        <Card className="glassmorphism mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search" className="text-white mb-2 block">Search Certificates</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search by name, certificate ID, course, or college..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
              <div className="md:w-48">
                <Label htmlFor="status" className="text-white mb-2 block">Filter by Status</Label>
                <select
                  id="status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="admin-select w-full p-2 rounded-md bg-white/10 border border-white/20 text-white"
                >
                  <option value="all" style={{ backgroundColor: '#000000', color: 'white' }}>All Status</option>
                  <option value="active" style={{ backgroundColor: '#000000', color: 'white' }}>Active</option>
                  <option value="inactive" style={{ backgroundColor: '#000000', color: 'white' }}>Inactive</option>
                  <option value="suspended" style={{ backgroundColor: '#000000', color: 'white' }}>Suspended</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Certificates Table */}
        <Card className="glassmorphism">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Certificate Management
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Manage and monitor all certificates in the system
                </CardDescription>
              </div>
              {filteredCertificates.length > 0 && (
                <Button
                  onClick={exportToExcel}
                  variant="outline"
                  className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export ({filteredCertificates.length})
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 text-psyco-green-DEFAULT animate-spin" />
                <span className="ml-2 text-white">Loading certificates...</span>
              </div>
            ) : filteredCertificates.length === 0 ? (
              <div className="text-center py-12">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">No certificates found</p>
                <p className="text-gray-500 text-sm">Try adjusting your search or filter criteria</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-left py-3 px-4 text-white font-medium">Certificate ID</th>
                      <th className="text-left py-3 px-4 text-white font-medium">Student Name</th>
                      <th className="text-left py-3 px-4 text-white font-medium">Course</th>
                      <th className="text-left py-3 px-4 text-white font-medium">College</th>
                      <th className="text-left py-3 px-4 text-white font-medium">Status</th>
                      <th className="text-left py-3 px-4 text-white font-medium">Created</th>
                      <th className="text-left py-3 px-4 text-white font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCertificates.map((certificate) => (
                      <tr key={certificate.id} className="border-b border-white/5 hover:bg-white/5">
                        <td className="py-3 px-4">
                          <code className="text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/10 px-2 py-1 rounded text-sm">
                            {certificate.certificateId}
                          </code>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-white">{certificate.name}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <GraduationCap className="h-4 w-4 text-blue-400" />
                            <span className="text-white">{certificate.courseName}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-300">{certificate.college}</td>
                        <td className="py-3 px-4">
                          <Badge
                            variant={certificate.status === 'active' ? 'default' : 'secondary'}
                            className={
                              certificate.status === 'active'
                                ? 'bg-green-500/20 text-green-400 border-green-500/30'
                                : 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                            }
                          >
                            {certificate.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4 text-gray-300 text-sm">
                          {formatTimestamp(certificate.createdAt)}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setViewingId(certificate.id)}
                              className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => startEdit(certificate)}
                              className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteCertificate(certificate.id, certificate.certificateId)}
                              disabled={isDeleting === certificate.id}
                              className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                            >
                              {isDeleting === certificate.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Certificate Modal */}
        {editingId && (
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                cancelEdit();
              }
            }}
          >
            <Card className="w-full max-w-2xl glassmorphism max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Edit className="h-5 w-5" />
                    Edit Certificate
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={cancelEdit}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <form onSubmit={(e) => { e.preventDefault(); saveChanges(); }} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-name" className="text-white">Student Name</Label>
                      <Input
                        id="edit-name"
                        type="text"
                        value={editForm.name || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-age" className="text-white">Age</Label>
                      <Input
                        id="edit-age"
                        type="number"
                        value={editForm.age || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, age: parseInt(e.target.value) || 0 }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-course" className="text-white">Course Name</Label>
                      <Input
                        id="edit-course"
                        type="text"
                        value={editForm.courseName || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, courseName: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-college" className="text-white">College</Label>
                      <Input
                        id="edit-college"
                        type="text"
                        value={editForm.college || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, college: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-country" className="text-white">Country</Label>
                      <Input
                        id="edit-country"
                        type="text"
                        value={editForm.country || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, country: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-state" className="text-white">State</Label>
                      <Input
                        id="edit-state"
                        type="text"
                        value={editForm.state || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, state: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-course-start" className="text-white">Course Start Date</Label>
                      <Input
                        id="edit-course-start"
                        type="date"
                        value={editForm.courseStartDate || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, courseStartDate: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-course-joining" className="text-white">Course Joining Date</Label>
                      <Input
                        id="edit-course-joining"
                        type="date"
                        value={editForm.courseJoiningDate || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, courseJoiningDate: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-course-end" className="text-white">Course End Date</Label>
                      <Input
                        id="edit-course-end"
                        type="date"
                        value={editForm.courseEndDate || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, courseEndDate: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-cert-provide" className="text-white">Certificate Provide Date</Label>
                      <Input
                        id="edit-cert-provide"
                        type="date"
                        value={editForm.certificateProvideDate || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, certificateProvideDate: e.target.value }))}
                        className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-status" className="text-white">Status</Label>
                      <select
                        id="edit-status"
                        value={editForm.status || 'active'}
                        onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value }))}
                        className="admin-select mt-1 w-full p-2 rounded-md bg-white/10 border border-white/20 text-white"
                        required
                      >
                        <option value="active" style={{ backgroundColor: '#000000', color: 'white' }}>Active</option>
                        <option value="inactive" style={{ backgroundColor: '#000000', color: 'white' }}>Inactive</option>
                        <option value="suspended" style={{ backgroundColor: '#000000', color: 'white' }}>Suspended</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={cancelEdit}
                      className="border-gray-500/50 text-gray-400 hover:bg-gray-500/10"
                    >
                      <X className="mr-2 h-4 w-4" />
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

        {/* View Certificate Modal */}
        {viewingId && (
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setViewingId(null);
              }
            }}
          >
            <Card className="w-full max-w-2xl glassmorphism max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Certificate Details
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewingId(null)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {(() => {
                  const certificate = certificates.find(c => c.id === viewingId);
                  if (!certificate) return <p className="text-gray-400">Certificate not found</p>;

                  return (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label className="text-white">Certificate ID</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20">
                            <code className="text-psyco-green-DEFAULT">{certificate.certificateId}</code>
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Status</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20">
                            <Badge
                              variant={certificate.status === 'active' ? 'default' : 'secondary'}
                              className={
                                certificate.status === 'active'
                                  ? 'bg-green-500/20 text-green-400 border-green-500/30'
                                  : 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                              }
                            >
                              {certificate.status}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Student Name</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.name}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Age</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.age}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Course Name</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.courseName}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">College</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.college}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Country</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.country}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">State</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.state}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Course Start Date</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {formatDate(certificate.courseStartDate || '')}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Course Joining Date</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {formatDate(certificate.courseJoiningDate || '')}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Course End Date</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {formatDate(certificate.courseEndDate)}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Certificate Provide Date</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {formatDate(certificate.certificateProvideDate)}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Created At</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {formatTimestamp(certificate.createdAt)}
                          </div>
                        </div>
                        <div>
                          <Label className="text-white">Created By</Label>
                          <div className="mt-1 p-2 bg-white/10 rounded border border-white/20 text-white">
                            {certificate.createdBy}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
